
import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, Tabs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { toast } from "@/hooks/use-toast";
import { 
  PenTool, 
  Upload,
  Sparkles,
  Eye,
  Save,
  Send,
  Instagram,
  Twitter,
  Linkedin,
  Youtube,
  Calendar,
  Clock,
  Hash,
  Image as ImageIcon,
  Video,
  Type,
  Lightbulb,
  Target,
  TrendingUp,
  Zap,
  CheckCircle,
  AlertCircle,
  BarChart3,
  MessageCircle,
  Heart,
  Share2,
  Wand2
} from 'lucide-react';

const ContentEditor = () => {
  const [content, setContent] = useState({
    title: '',
    body: '',
    hashtags: '',
    platforms: {
      instagram: true,
      tiktok: false,
      linkedin: false,
      twitter: false
    },
    publishType: 'now',
    scheduledTime: ''
  });

  const [aiAnalysis, setAiAnalysis] = useState({
    overallScore: 8.5,
    simplicity: 8.2,
    specificity: 7.8,
    emotion: 9.1,
    hookQuality: 8.7,
    predictions: {
      estimatedReach: 15600,
      estimatedEngagement: 1340,
      viralPotential: 'high'
    }
  });

  const [aiSuggestions, setAiSuggestions] = useState([
    { 
      type: 'title', 
      priority: 'high',
      text: '建议在标题中加入数字，如"5个技巧"可提升点击率38%',
      impact: '+38% 点击率'
    },
    { 
      type: 'hashtag', 
      priority: 'medium',
      text: '推荐热门标签：#内容营销 #社交媒体 #创作技巧',
      impact: '+25% 覆盖率'
    },
    { 
      type: 'timing', 
      priority: 'high',
      text: '最佳发布时间：今天下午3-5点（用户活跃度峰值）',
      impact: '+42% 互动率'
    },
    { 
      type: 'engagement', 
      priority: 'medium',
      text: '在内容末尾添加问题可以提高评论互动率56%',
      impact: '+56% 评论数'
    }
  ]);

  const platforms = [
    { 
      id: 'instagram', 
      name: 'Instagram', 
      icon: Instagram, 
      color: 'bg-pink-500', 
      active: content.platforms.instagram,
      optimalLength: '2200字符',
      bestTime: '15:00-17:00',
      estimatedReach: '12.5K'
    },
    { 
      id: 'tiktok', 
      name: 'TikTok', 
      icon: Youtube, 
      color: 'bg-red-500', 
      active: content.platforms.tiktok,
      optimalLength: '150字符',
      bestTime: '19:00-21:00',
      estimatedReach: '8.7K'
    },
    { 
      id: 'linkedin', 
      name: 'LinkedIn', 
      icon: Linkedin, 
      color: 'bg-blue-600', 
      active: content.platforms.linkedin,
      optimalLength: '1300字符',
      bestTime: '08:00-10:00',
      estimatedReach: '3.2K'
    },
    { 
      id: 'twitter', 
      name: 'Twitter', 
      icon: Twitter, 
      color: 'bg-blue-400', 
      active: content.platforms.twitter,
      optimalLength: '280字符',
      bestTime: '12:00-15:00',
      estimatedReach: '5.1K'
    }
  ];

  const handlePlatformToggle = (platformId: string) => {
    setContent(prev => ({
      ...prev,
      platforms: {
        ...prev.platforms,
        [platformId]: !prev.platforms[platformId as keyof typeof prev.platforms]
      }
    }));
  };

  const handleAiOptimize = () => {
    toast({
      title: "AI优化完成",
      description: "内容已根据最佳实践进行优化",
    });
  };

  const handlePublish = () => {
    if (!content.title || !content.body) {
      toast({
        title: "请完善内容",
        description: "标题和正文内容不能为空",
        variant: "destructive",
      });
      return;
    }

    const selectedPlatforms = Object.entries(content.platforms)
      .filter(([_, active]) => active)
      .map(([platform, _]) => platform);

    if (selectedPlatforms.length === 0) {
      toast({
        title: "请选择发布平台",
        description: "至少选择一个社交媒体平台",
        variant: "destructive",
      });
      return;
    }

    toast({
      title: "内容发布成功",
      description: `已发布到 ${selectedPlatforms.length} 个平台`,
    });
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-600 bg-red-50 border-red-200';
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'low': return 'text-green-600 bg-green-50 border-green-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  return (
    <div className="space-y-6 p-6">
      {/* 顶部标题栏 */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold text-gray-900">AI内容创作工作台</h2>
          <p className="text-gray-600 mt-1">智能优化您的内容创作流程</p>
        </div>
        <div className="flex space-x-3">
          <Button variant="outline">
            <Save className="w-4 h-4 mr-2" />
            保存草稿
          </Button>
          <Button variant="outline">
            <Eye className="w-4 h-4 mr-2" />
            预览效果
          </Button>
          <Button onClick={handlePublish} className="bg-blue-600 hover:bg-blue-700">
            <Send className="w-4 h-4 mr-2" />
            发布内容
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
        {/* 主要编辑区域 */}
        <div className="xl:col-span-3 space-y-6">
          {/* 内容编辑器 */}
          <Card className="border-0 shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center">
                  <PenTool className="w-5 h-5 mr-2" />
                  智能内容编辑器
                </div>
                <Button variant="outline" size="sm" onClick={handleAiOptimize}>
                  <Wand2 className="w-4 h-4 mr-2" />
                  AI优化
                </Button>
              </CardTitle>
              <CardDescription>使用AI助手创建高质量内容</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="title">内容标题</Label>
                <Input
                  id="title"
                  placeholder="输入吸引人的标题..."
                  value={content.title}
                  onChange={(e) => setContent({...content, title: e.target.value})}
                  className="text-lg"
                />
                <div className="flex justify-between text-sm text-gray-500">
                  <span>{content.title.length} 字符</span>
                  <span>建议长度：20-60字符</span>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="content">内容正文</Label>
                <Textarea
                  id="content"
                  placeholder="分享您的精彩内容..."
                  className="min-h-[200px] resize-none text-base"
                  value={content.body}
                  onChange={(e) => setContent({...content, body: e.target.value})}
                />
                <div className="flex justify-between text-sm text-gray-500">
                  <span>{content.body.length} 字符</span>
                  <span>各平台最佳长度建议在右侧查看</span>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="hashtags">标签设置</Label>
                <Input
                  id="hashtags"
                  placeholder="#标签1 #标签2 #标签3"
                  value={content.hashtags}
                  onChange={(e) => setContent({...content, hashtags: e.target.value})}
                />
                <div className="flex items-center space-x-2 text-sm text-gray-500">
                  <Hash className="w-4 h-4" />
                  <span>AI建议：#内容营销 #社交媒体 #创作技巧</span>
                </div>
              </div>

              {/* 媒体文件上传区域 */}
              <div className="space-y-4">
                <Label>媒体文件</Label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors">
                  <div className="flex flex-col items-center">
                    <Upload className="w-12 h-12 text-gray-400 mb-4" />
                    <p className="text-gray-600 mb-2 font-medium">拖拽文件到这里或点击上传</p>
                    <p className="text-sm text-gray-500 mb-4">支持图片、视频格式，最大 50MB</p>
                    <div className="flex space-x-3">
                      <Button variant="outline" size="sm">
                        <ImageIcon className="w-4 h-4 mr-2" />
                        上传图片
                      </Button>
                      <Button variant="outline" size="sm">
                        <Video className="w-4 h-4 mr-2" />
                        上传视频
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 平台发布设置 */}
          <Card className="border-0 shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Send className="w-5 h-5 mr-2" />
                发布设置
              </CardTitle>
              <CardDescription>选择发布平台和时间安排</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* 平台选择 */}
              <div className="space-y-3">
                <Label>发布平台</Label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {platforms.map((platform) => (
                    <div
                      key={platform.id}
                      className={`flex items-center justify-between p-4 rounded-lg border-2 transition-all ${
                        platform.active ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <div className="flex items-center space-x-3">
                        <div className={`p-2 rounded-lg ${platform.color} text-white`}>
                          <platform.icon className="w-5 h-5" />
                        </div>
                        <div>
                          <p className="font-semibold text-gray-900">{platform.name}</p>
                          <p className="text-sm text-gray-600">预计覆盖: {platform.estimatedReach}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <Switch
                          checked={platform.active}
                          onCheckedChange={() => handlePlatformToggle(platform.id)}
                        />
                        {platform.active && (
                          <p className="text-xs text-gray-500 mt-1">{platform.bestTime}</p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <Separator />

              {/* 发布时间设置 */}
              <div className="space-y-4">
                <Label>发布时间</Label>
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <input
                      type="radio"
                      id="now"
                      name="publishType"
                      value="now"
                      checked={content.publishType === 'now'}
                      onChange={(e) => setContent({...content, publishType: e.target.value})}
                      className="w-4 h-4 text-blue-600"
                    />
                    <Label htmlFor="now" className="flex items-center cursor-pointer">
                      <Zap className="w-4 h-4 mr-2 text-green-500" />
                      立即发布
                    </Label>
                  </div>
                  <div className="flex items-center space-x-3">
                    <input
                      type="radio"
                      id="scheduled"
                      name="publishType"
                      value="scheduled"
                      checked={content.publishType === 'scheduled'}
                      onChange={(e) => setContent({...content, publishType: e.target.value})}
                      className="w-4 h-4 text-blue-600"
                    />
                    <Label htmlFor="scheduled" className="flex items-center cursor-pointer">
                      <Calendar className="w-4 h-4 mr-2 text-blue-500" />
                      智能定时发布
                    </Label>
                  </div>
                  {content.publishType === 'scheduled' && (
                    <div className="ml-7 space-y-2">
                      <Input
                        type="datetime-local"
                        value={content.scheduledTime}
                        onChange={(e) => setContent({...content, scheduledTime: e.target.value})}
                        className="max-w-xs"
                      />
                      <p className="text-sm text-blue-600">💡 AI建议：今天15:00-17:00效果最佳</p>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 右侧AI助手区域 */}
        <div className="space-y-6">
          {/* AI内容分析 */}
          <Card className="border-0 shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart3 className="w-5 h-5 mr-2" />
                AI内容分析
              </CardTitle>
              <CardDescription>实时内容质量评估</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 mb-1">
                  {aiAnalysis.overallScore}/10
                </div>
                <p className="text-sm text-gray-600">综合内容评分</p>
              </div>
              
              <div className="space-y-3">
                <div>
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-sm text-gray-700">简洁性</span>
                    <span className="text-sm font-medium">{aiAnalysis.simplicity}/10</span>
                  </div>
                  <Progress value={aiAnalysis.simplicity * 10} className="h-2" />
                </div>
                
                <div>
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-sm text-gray-700">具体性</span>
                    <span className="text-sm font-medium">{aiAnalysis.specificity}/10</span>
                  </div>
                  <Progress value={aiAnalysis.specificity * 10} className="h-2" />
                </div>
                
                <div>
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-sm text-gray-700">情感触发</span>
                    <span className="text-sm font-medium">{aiAnalysis.emotion}/10</span>
                  </div>
                  <Progress value={aiAnalysis.emotion * 10} className="h-2" />
                </div>
                
                <div>
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-sm text-gray-700">开头吸引力</span>
                    <span className="text-sm font-medium">{aiAnalysis.hookQuality}/10</span>
                  </div>
                  <Progress value={aiAnalysis.hookQuality * 10} className="h-2" />
                </div>
              </div>

              <Separator />

              <div className="space-y-2">
                <h4 className="font-medium text-gray-900">预测表现</h4>
                <div className="grid grid-cols-2 gap-4 text-center">
                  <div className="p-2 bg-blue-50 rounded">
                    <div className="text-lg font-bold text-blue-600">
                      {aiAnalysis.predictions.estimatedReach.toLocaleString()}
                    </div>
                    <div className="text-xs text-gray-600">预计覆盖</div>
                  </div>
                  <div className="p-2 bg-green-50 rounded">
                    <div className="text-lg font-bold text-green-600">
                      {aiAnalysis.predictions.estimatedEngagement.toLocaleString()}
                    </div>
                    <div className="text-xs text-gray-600">预计互动</div>
                  </div>
                </div>
                <div className="text-center">
                  <Badge className={`${
                    aiAnalysis.predictions.viralPotential === 'high' 
                      ? 'bg-red-100 text-red-800' 
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {aiAnalysis.predictions.viralPotential === 'high' ? '🔥 高爆款潜力' : '⚡ 中等传播潜力'}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* AI优化建议 */}
          <Card className="border-0 shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Sparkles className="w-5 h-5 mr-2" />
                AI优化建议
              </CardTitle>
              <CardDescription>基于数据分析的改进建议</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              {aiSuggestions.map((suggestion, index) => (
                <div key={index} className={`p-3 rounded-lg border ${getPriorityColor(suggestion.priority)}`}>
                  <div className="flex items-start space-x-2">
                    <div className="p-1 rounded">
                      {suggestion.type === 'title' && <Type className="w-3 h-3" />}
                      {suggestion.type === 'hashtag' && <Hash className="w-3 h-3" />}
                      {suggestion.type === 'timing' && <Clock className="w-3 h-3" />}
                      {suggestion.type === 'engagement' && <MessageCircle className="w-3 h-3" />}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-1">
                        <Badge variant="outline" className="text-xs">
                          {suggestion.priority === 'high' ? '高优先级' : suggestion.priority === 'medium' ? '中优先级' : '低优先级'}
                        </Badge>
                        <span className="text-xs font-medium text-green-600">{suggestion.impact}</span>
                      </div>
                      <p className="text-sm">{suggestion.text}</p>
                      <Button variant="link" size="sm" className="p-0 h-auto mt-1 text-xs">
                        一键应用
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
              <Button variant="outline" size="sm" className="w-full">
                <Lightbulb className="w-4 h-4 mr-2" />
                获取更多建议
              </Button>
            </CardContent>
          </Card>

          {/* 平台预览 */}
          <Card className="border-0 shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Eye className="w-5 h-5 mr-2" />
                实时预览
              </CardTitle>
              <CardDescription>查看内容在各平台的效果</CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="instagram" className="w-full">
                <TabsList className="grid grid-cols-2 w-full">
                  <TabsTrigger value="instagram" className="flex items-center text-xs">
                    <Instagram className="w-3 h-3 mr-1" />
                    Instagram
                  </TabsTrigger>
                  <TabsTrigger value="tiktok" className="flex items-center text-xs">
                    <Youtube className="w-3 h-3 mr-1" />
                    TikTok
                  </TabsTrigger>
                </TabsList>
                <TabsContent value="instagram" className="mt-4">
                  <div className="border rounded-lg p-3 bg-white">
                    <div className="flex items-center space-x-2 mb-2">
                      <div className="w-6 h-6 bg-gradient-to-tr from-yellow-400 to-purple-600 rounded-full"></div>
                      <div>
                        <p className="font-medium text-sm">your_username</p>
                        <p className="text-xs text-gray-500">Instagram</p>
                      </div>
                    </div>
                    <div className="aspect-square bg-gray-200 rounded mb-2 flex items-center justify-center">
                      <ImageIcon className="w-8 h-8 text-gray-400" />
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm font-medium">{content.title || '标题预览...'}</p>
                      <p className="text-sm text-gray-600 line-clamp-3">{content.body || '内容预览...'}</p>
                      <p className="text-sm text-blue-600">{content.hashtags || '#标签预览'}</p>
                    </div>
                  </div>
                </TabsContent>
                <TabsContent value="tiktok" className="mt-4">
                  <div className="border rounded-lg p-3 bg-black text-white">
                    <div className="aspect-[9/16] bg-gray-800 rounded mb-2 flex items-center justify-center">
                      <Video className="w-8 h-8 text-gray-400" />
                    </div>
                    <div className="space-y-1">
                      <p className="text-sm font-medium">{content.title || '标题预览...'}</p>
                      <p className="text-sm text-gray-300">{content.hashtags || '#标签预览'}</p>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ContentEditor;
