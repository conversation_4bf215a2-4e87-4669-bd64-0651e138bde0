'use client';

import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { useRouter } from 'next/navigation';
import { 
  PenTool, 
  BarChart3, 
  Calendar, 
  Target,
  Users,
  TrendingUp,
  Check,
  Star,
  ArrowRight,
  PlayCircle
} from 'lucide-react';

export const ContentFlowLandingPage = () => {
  const router = useRouter();

  const handleGetStarted = () => {
    router.push('/app/login');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-blue-600 rounded-xl flex items-center justify-center">
                <PenTool className="w-6 h-6 text-white" />
              </div>
              <h1 className="text-2xl font-bold text-gray-900">ContentFlow</h1>
            </div>
            <div className="hidden md:flex items-center space-x-8">
              <a href="#features" className="text-gray-600 hover:text-gray-900 transition-colors">功能特色</a>
              <a href="#pricing" className="text-gray-600 hover:text-gray-900 transition-colors">定价</a>
              <a href="#faq" className="text-gray-600 hover:text-gray-900 transition-colors">常见问题</a>
              <Button onClick={handleGetStarted} className="bg-blue-600 hover:bg-blue-700">
                开始使用
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center">
            <Badge className="mb-4 bg-blue-100 text-blue-800 hover:bg-blue-100">
              <Star className="w-4 h-4 mr-1" />
              AI驱动的内容创作平台
            </Badge>
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              让内容创作变得
              <span className="text-blue-600"> 简单高效</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              统一管理多个社交平台，AI智能优化内容，数据驱动决策。
              专为内容创作者打造的一站式解决方案。
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                onClick={handleGetStarted}
                size="lg" 
                className="bg-blue-600 hover:bg-blue-700 text-lg px-8 py-4"
              >
                免费开始使用
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
              <Button 
                variant="outline" 
                size="lg" 
                className="text-lg px-8 py-4"
              >
                <PlayCircle className="w-5 h-5 mr-2" />
                观看演示
              </Button>
            </div>
            <div className="mt-12 flex items-center justify-center space-x-8 text-sm text-gray-500">
              <div className="flex items-center">
                <Check className="w-4 h-4 text-green-500 mr-2" />
                14天免费试用
              </div>
              <div className="flex items-center">
                <Check className="w-4 h-4 text-green-500 mr-2" />
                无需信用卡
              </div>
              <div className="flex items-center">
                <Check className="w-4 h-4 text-green-500 mr-2" />
                随时取消
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              为什么选择 ContentFlow？
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              集成最新AI技术，简化内容创作流程，提升创作效率和内容表现
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card className="p-6 hover:shadow-lg transition-shadow">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                <PenTool className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">AI智能创作</h3>
              <p className="text-gray-600">
                基于GPT-4的智能文案生成，自动优化标题和内容，提升互动率和转化效果
              </p>
            </Card>

            <Card className="p-6 hover:shadow-lg transition-shadow">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                <Target className="w-6 h-6 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">多平台发布</h3>
              <p className="text-gray-600">
                一键发布到Instagram、TikTok、LinkedIn等多个平台，自动适配不同格式要求
              </p>
            </Card>

            <Card className="p-6 hover:shadow-lg transition-shadow">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                <BarChart3 className="w-6 h-6 text-purple-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">数据分析</h3>
              <p className="text-gray-600">
                深度分析内容表现，提供优化建议，帮助您制作更受欢迎的内容
              </p>
            </Card>

            <Card className="p-6 hover:shadow-lg transition-shadow">
              <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mb-4">
                <Calendar className="w-6 h-6 text-orange-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">智能排程</h3>
              <p className="text-gray-600">
                基于受众活跃时间的智能发布调度，确保内容在最佳时机触达用户
              </p>
            </Card>

            <Card className="p-6 hover:shadow-lg transition-shadow">
              <div className="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4">
                <TrendingUp className="w-6 h-6 text-red-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">增长预测</h3>
              <p className="text-gray-600">
                AI预测内容表现，识别高潜力内容，帮助您专注于最有效的创作方向
              </p>
            </Card>

            <Card className="p-6 hover:shadow-lg transition-shadow">
              <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mb-4">
                <Users className="w-6 h-6 text-indigo-600" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">团队协作</h3>
              <p className="text-gray-600">
                支持团队成员协作，审核流程管理，让内容创作更加高效有序
              </p>
            </Card>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section id="faq" className="py-20 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              常见问题
            </h2>
            <p className="text-xl text-gray-600">
              解答您关于ContentFlow的疑问
            </p>
          </div>

          <Accordion type="single" collapsible className="w-full">
            <AccordionItem value="item-1">
              <AccordionTrigger className="text-left">
                ContentFlow支持哪些社交媒体平台？
              </AccordionTrigger>
              <AccordionContent>
                目前支持Instagram、TikTok、LinkedIn和Twitter等主流平台。我们正在不断添加更多平台支持，包括YouTube、小红书等。
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="item-2">
              <AccordionTrigger className="text-left">
                AI文案生成的质量如何？
              </AccordionTrigger>
              <AccordionContent>
                我们使用最新的GPT-4模型，结合您的品牌声音和历史数据训练，生成的文案经过优化，平均可提升30%的互动率。
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="item-3">
              <AccordionTrigger className="text-left">
                免费版本有什么限制？
              </AccordionTrigger>
              <AccordionContent>
                免费版支持连接2个社交账户，每月发布50条内容，包含基础数据分析功能。升级到付费版本可享受更多功能和无限制使用。
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="item-4">
              <AccordionTrigger className="text-left">
                数据安全如何保障？
              </AccordionTrigger>
              <AccordionContent>
                我们采用银行级加密技术，所有数据传输使用SSL加密，严格遵守GDPR等数据保护法规，绝不会泄露或滥用您的数据。
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="item-5">
              <AccordionTrigger className="text-left">
                可以随时取消订阅吗？
              </AccordionTrigger>
              <AccordionContent>
                当然可以。您可以随时在账户设置中取消订阅，取消后仍可使用到当前计费周期结束。我们不收取任何取消费用。
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="item-6">
              <AccordionTrigger className="text-left">
                是否提供技术支持？
              </AccordionTrigger>
              <AccordionContent>
                我们提供全天候客服支持，包括在线聊天、邮件支持和视频教程。付费用户还可享受优先技术支持服务。
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-blue-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            准备好提升您的内容创作了吗？
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            加入成千上万的内容创作者，开始您的ContentFlow之旅
          </p>
          <Button
            onClick={handleGetStarted}
            size="lg"
            className="bg-white text-blue-600 hover:bg-gray-100 text-lg px-8 py-4"
          >
            立即免费注册
            <ArrowRight className="w-5 h-5 ml-2" />
          </Button>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <PenTool className="w-4 h-4 text-white" />
                </div>
                <h3 className="text-xl font-bold">ContentFlow</h3>
              </div>
              <p className="text-gray-400">
                AI驱动的内容创作与发布管理平台
              </p>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">产品</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">功能特色</a></li>
                <li><a href="#" className="hover:text-white transition-colors">定价方案</a></li>
                <li><a href="#" className="hover:text-white transition-colors">API文档</a></li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">支持</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">帮助中心</a></li>
                <li><a href="#" className="hover:text-white transition-colors">联系我们</a></li>
                <li><a href="#" className="hover:text-white transition-colors">状态页面</a></li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">公司</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white transition-colors">关于我们</a></li>
                <li><a href="#" className="hover:text-white transition-colors">隐私政策</a></li>
                <li><a href="#" className="hover:text-white transition-colors">服务条款</a></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 ContentFlow. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};
