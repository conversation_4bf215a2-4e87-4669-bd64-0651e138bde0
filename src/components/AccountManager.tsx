
import { useState } from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { toast } from "@/hooks/use-toast";
import { 
  Plus, 
  Settings, 
  RefreshCw,
  Unlink,
  Link,
  AlertCircle,
  CheckCircle,
  Users,
  TrendingUp,
  Calendar,
  Shield,
  Key,
  Bell,
  Instagram,
  Twitter,
  Linkedin,
  Youtube,
  Facebook,
  Globe
} from 'lucide-react';

const AccountManager = () => {
  const [connectedAccounts, setConnectedAccounts] = useState([
    {
      id: 1,
      platform: 'Instagram',
      username: '@your_username',
      displayName: 'Your Instagram',
      followers: 15200,
      isActive: true,
      lastSync: '2分钟前',
      icon: Instagram,
      color: 'bg-pink-500',
      engagement: 8.2,
      postsCount: 234,
      status: 'connected',
      permissions: ['read', 'write', 'analytics']
    },
    {
      id: 2,
      platform: 'TikTok',
      username: '@your_tiktok',
      displayName: 'Your TikTok',
      followers: 8700,
      isActive: true,
      lastSync: '5分钟前',
      icon: Youtube,
      color: 'bg-red-500',
      engagement: 12.4,
      postsCount: 89,
      status: 'connected',
      permissions: ['read', 'write']
    },
    {
      id: 3,
      platform: 'LinkedIn',
      username: '@your_linkedin',
      displayName: 'Your LinkedIn',
      followers: 3200,
      isActive: false,
      lastSync: '2小时前',
      icon: Linkedin,
      color: 'bg-blue-600',
      engagement: 6.1,
      postsCount: 156,
      status: 'error',
      permissions: ['read', 'write', 'analytics']
    },
    {
      id: 4,
      platform: 'Twitter',
      username: '@your_twitter',
      displayName: 'Your Twitter',
      followers: 5100,
      isActive: true,
      lastSync: '10分钟前',
      icon: Twitter,
      color: 'bg-blue-400',
      engagement: 4.8,
      postsCount: 445,
      status: 'connected',
      permissions: ['read', 'write']
    }
  ]);

  const availablePlatforms = [
    {
      platform: 'YouTube',
      description: '连接您的YouTube频道以发布视频内容',
      icon: Youtube,
      color: 'bg-red-600',
      features: ['视频发布', '数据分析', '评论管理']
    },
    {
      platform: 'Facebook',
      description: '连接您的Facebook页面以扩大覆盖面',
      icon: Facebook,
      color: 'bg-blue-800',
      features: ['页面发布', '广告管理', '受众洞察']
    },
    {
      platform: 'Pinterest',
      description: '连接Pinterest以发布图片和创意内容',
      icon: Globe,
      color: 'bg-red-500',
      features: ['图片发布', '创意分析', '趋势追踪']
    }
  ];

  const handleToggleAccount = (accountId: number) => {
    setConnectedAccounts(prev => 
      prev.map(account => 
        account.id === accountId 
          ? { ...account, isActive: !account.isActive }
          : account
      )
    );
    toast({
      title: "账户状态已更新",
      description: "账户设置已保存",
    });
  };

  const handleReconnect = (accountId: number) => {
    const account = connectedAccounts.find(acc => acc.id === accountId);
    toast({
      title: "重新连接账户",
      description: `正在重新连接 ${account?.platform} 账户...`,
    });
  };

  const handleDisconnect = (accountId: number) => {
    const account = connectedAccounts.find(acc => acc.id === accountId);
    toast({
      title: "账户已断开",
      description: `${account?.platform} 账户已断开连接`,
      variant: "destructive",
    });
  };

  const handleConnectPlatform = (platform: string) => {
    toast({
      title: "连接新平台",
      description: `正在连接到 ${platform}...`,
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'connected':
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />已连接</Badge>;
      case 'error':
        return <Badge className="bg-red-100 text-red-800"><AlertCircle className="w-3 h-3 mr-1" />需要重新授权</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">未知状态</Badge>;
    }
  };

  const getPermissionText = (permissions: string[]) => {
    const permissionMap: { [key: string]: string } = {
      read: '读取',
      write: '发布',
      analytics: '分析'
    };
    return permissions.map(p => permissionMap[p] || p).join(', ');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">账户管理</h2>
          <p className="text-gray-600">管理您的社交媒体账户连接和权限</p>
        </div>
        <Button className="bg-blue-600 hover:bg-blue-700">
          <Plus className="w-4 h-4 mr-2" />
          连接新账户
        </Button>
      </div>

      {/* Account Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="border-0 shadow-sm">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">已连接账户</p>
                <p className="text-2xl font-bold text-gray-900">{connectedAccounts.length}</p>
              </div>
              <div className="p-3 rounded-full bg-blue-100 text-blue-600">
                <Link className="w-6 h-6" />
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="border-0 shadow-sm">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">总关注者</p>
                <p className="text-2xl font-bold text-gray-900">
                  {(connectedAccounts.reduce((sum, account) => sum + account.followers, 0) / 1000).toFixed(1)}K
                </p>
              </div>
              <div className="p-3 rounded-full bg-green-100 text-green-600">
                <Users className="w-6 h-6" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">活跃账户</p>
                <p className="text-2xl font-bold text-gray-900">
                  {connectedAccounts.filter(account => account.isActive).length}
                </p>
              </div>
              <div className="p-3 rounded-full bg-purple-100 text-purple-600">
                <CheckCircle className="w-6 h-6" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">平均互动率</p>
                <p className="text-2xl font-bold text-gray-900">
                  {(connectedAccounts.reduce((sum, account) => sum + account.engagement, 0) / connectedAccounts.length).toFixed(1)}%
                </p>
              </div>
              <div className="p-3 rounded-full bg-orange-100 text-orange-600">
                <TrendingUp className="w-6 h-6" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Connected Accounts */}
      <Card className="border-0 shadow-sm">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Link className="w-5 h-5 mr-2" />
            已连接账户
          </CardTitle>
          <CardDescription>管理您的社交媒体账户连接</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {connectedAccounts.map((account) => (
            <div key={account.id} className="p-6 rounded-lg border border-gray-200">
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-4">
                  <div className={`p-3 rounded-lg ${account.color} text-white`}>
                    <account.icon className="w-6 h-6" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900">{account.platform}</h3>
                      {getStatusBadge(account.status)}
                      <Switch
                        checked={account.isActive}
                        onCheckedChange={() => handleToggleAccount(account.id)}
                      />
                    </div>
                    <p className="text-gray-600 mb-2">{account.username}</p>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                      <div>
                        <p className="text-sm text-gray-500">关注者</p>
                        <p className="font-semibold">{account.followers.toLocaleString()}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">互动率</p>
                        <p className="font-semibold">{account.engagement}%</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">内容数量</p>
                        <p className="font-semibold">{account.postsCount}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500">最后同步</p>
                        <p className="font-semibold">{account.lastSync}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      <div className="flex items-center space-x-1">
                        <Shield className="w-4 h-4" />
                        <span>权限: {getPermissionText(account.permissions)}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="flex flex-col space-y-2">
                  <Button variant="outline" size="sm">
                    <Settings className="w-4 h-4 mr-2" />
                    设置
                  </Button>
                  {account.status === 'error' ? (
                    <Button variant="outline" size="sm" onClick={() => handleReconnect(account.id)}>
                      <RefreshCw className="w-4 h-4 mr-2" />
                      重新连接
                    </Button>
                  ) : (
                    <Button variant="outline" size="sm" onClick={() => handleReconnect(account.id)}>
                      <RefreshCw className="w-4 h-4 mr-2" />
                      同步数据
                    </Button>
                  )}
                  <Button variant="outline" size="sm" onClick={() => handleDisconnect(account.id)}>
                    <Unlink className="w-4 h-4 mr-2" />
                    断开连接
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Available Platforms */}
      <Card className="border-0 shadow-sm">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Plus className="w-5 h-5 mr-2" />
            添加新平台
          </CardTitle>
          <CardDescription>连接更多社交媒体平台以扩大您的影响力</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {availablePlatforms.map((platform, index) => (
              <div key={index} className="p-6 rounded-lg border border-gray-200 hover:shadow-md transition-shadow">
                <div className="flex items-center space-x-3 mb-4">
                  <div className={`p-3 rounded-lg ${platform.color} text-white`}>
                    <platform.icon className="w-6 h-6" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900">{platform.platform}</h3>
                </div>
                <p className="text-gray-600 mb-4">{platform.description}</p>
                <div className="space-y-2 mb-4">
                  {platform.features.map((feature, idx) => (
                    <div key={idx} className="flex items-center space-x-2">
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      <span className="text-sm text-gray-600">{feature}</span>
                    </div>
                  ))}
                </div>
                <Button 
                  className="w-full" 
                  variant="outline"
                  onClick={() => handleConnectPlatform(platform.platform)}
                >
                  <Plus className="w-4 h-4 mr-2" />
                  连接 {platform.platform}
                </Button>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Account Settings */}
      <Card className="border-0 shadow-sm">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Settings className="w-5 h-5 mr-2" />
            全局设置
          </CardTitle>
          <CardDescription>配置账户管理的全局设置</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-gray-900">自动同步数据</h4>
              <p className="text-sm text-gray-600">定期同步所有连接账户的数据</p>
            </div>
            <Switch defaultChecked />
          </div>
          <Separator />
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-gray-900">连接状态通知</h4>
              <p className="text-sm text-gray-600">当账户连接出现问题时发送通知</p>
            </div>
            <Switch defaultChecked />
          </div>
          <Separator />
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-gray-900">数据备份</h4>
              <p className="text-sm text-gray-600">定期备份您的账户数据和设置</p>
            </div>
            <Switch defaultChecked />
          </div>
          <Separator />
          <div className="space-y-4">
            <h4 className="font-medium text-gray-900">API 使用限制</h4>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Instagram API</span>
                <span className="text-sm font-medium">780 / 1000 (78%)</span>
              </div>
              <Progress value={78} className="h-2" />
            </div>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Twitter API</span>
                <span className="text-sm font-medium">450 / 1000 (45%)</span>
              </div>
              <Progress value={45} className="h-2" />
            </div>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">LinkedIn API</span>
                <span className="text-sm font-medium">230 / 500 (46%)</span>
              </div>
              <Progress value={46} className="h-2" />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AccountManager;
