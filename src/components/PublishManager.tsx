
import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { toast } from "@/hooks/use-toast";
import { 
  Calendar, 
  Clock, 
  Send,
  Edit,
  Trash2,
  Copy,
  BarChart3,
  RefreshCw,
  Plus,
  Filter,
  Search,
  CheckCircle,
  XCircle,
  Pause,
  Play,
  Instagram,
  Twitter,
  Linkedin,
  Youtube,
  Eye,
  Heart,
  MessageCircle,
  Share2
} from 'lucide-react';

const PublishManager = () => {
  const [activeTab, setActiveTab] = useState('scheduled');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [platformFilter, setPlatformFilter] = useState('all');

  const scheduledPosts = [
    {
      id: 1,
      title: "内容营销的5个关键策略",
      excerpt: "在数字化时代，内容营销已成为品牌与消费者建立联系的重要桥梁...",
      scheduledTime: "2024-01-26 14:30",
      platforms: ["Instagram", "TikTok"],
      status: "scheduled",
      author: "您",
      tags: ["营销", "策略", "内容"],
      media: "image"
    },
    {
      id: 2,
      title: "如何制作病毒式传播的短视频",
      excerpt: "短视频内容创作的核心要素包括：吸引人的开头、清晰的价值传递...",
      scheduledTime: "2024-01-27 09:00",
      platforms: ["TikTok", "Instagram"],
      status: "scheduled",
      author: "您",
      tags: ["短视频", "创作", "传播"],
      media: "video"
    },
    {
      id: 3,
      title: "个人品牌建设的10个步骤",
      excerpt: "建立个人品牌是一个系统性的过程，需要持续的努力和策略规划...",
      scheduledTime: "2024-01-27 15:00",
      platforms: ["LinkedIn"],
      status: "scheduled",
      author: "您",
      tags: ["个人品牌", "建设", "策略"],
      media: "article"
    }
  ];

  const publishedPosts = [
    {
      id: 101,
      title: "LinkedIn个人品牌建设完整指南",
      excerpt: "在职场竞争激烈的今天，建立强大的个人品牌变得前所未有的重要...",
      publishedTime: "2024-01-25 15:30",
      platforms: ["LinkedIn"],
      status: "published",
      author: "您",
      tags: ["LinkedIn", "个人品牌", "职场"],
      media: "article",
      performance: {
        likes: 756,
        comments: 34,
        shares: 89,
        reach: 18234,
        engagement: 6.8
      }
    },
    {
      id: 102,
      title: "Instagram Reels制作技巧",
      excerpt: "Reels是Instagram上最受欢迎的内容格式之一，掌握制作技巧...",
      publishedTime: "2024-01-24 10:15",
      platforms: ["Instagram"],
      status: "published",
      author: "您",
      tags: ["Instagram", "Reels", "技巧"],
      media: "video",
      performance: {
        likes: 1234,
        comments: 89,
        shares: 45,
        reach: 25678,
        engagement: 8.2
      }
    },
    {
      id: 103,
      title: "社交媒体内容策划完整指南",
      excerpt: "成功的社交媒体营销需要系统的内容策划和执行策略...",
      publishedTime: "2024-01-23 14:20",
      platforms: ["Twitter", "LinkedIn"],
      status: "published",
      author: "您",
      tags: ["社交媒体", "内容策划", "营销"],
      media: "article",
      performance: {
        likes: 567,
        comments: 23,
        shares: 67,
        reach: 12456,
        engagement: 5.4
      }
    }
  ];

  const draftPosts = [
    {
      id: 201,
      title: "2024年社交媒体趋势预测",
      excerpt: "随着技术的不断发展，社交媒体领域也在经历着快速的变化...",
      lastModified: "2024-01-25 16:45",
      platforms: [],
      status: "draft",
      author: "您",
      tags: ["趋势", "预测", "2024"],
      media: "article"
    },
    {
      id: 202,
      title: "品牌故事的力量",
      excerpt: "每个成功的品牌背后都有一个引人入胜的故事...",
      lastModified: "2024-01-24 11:30",
      platforms: [],
      status: "draft",
      author: "您",
      tags: ["品牌", "故事", "营销"],
      media: "image"
    }
  ];

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'Instagram': return Instagram;
      case 'TikTok': return Youtube;
      case 'LinkedIn': return Linkedin;
      case 'Twitter': return Twitter;
      default: return Send;
    }
  };

  const getPlatformColor = (platform: string) => {
    switch (platform) {
      case 'Instagram': return 'bg-pink-500';
      case 'TikTok': return 'bg-red-500';
      case 'LinkedIn': return 'bg-blue-600';
      case 'Twitter': return 'bg-blue-400';
      default: return 'bg-gray-500';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return 'bg-yellow-100 text-yellow-800';
      case 'published': return 'bg-green-100 text-green-800';
      case 'draft': return 'bg-gray-100 text-gray-800';
      case 'failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'scheduled': return '待发布';
      case 'published': return '已发布';
      case 'draft': return '草稿';
      case 'failed': return '发布失败';
      default: return '未知';
    }
  };

  const handleEdit = (id: number) => {
    toast({
      title: "编辑内容",
      description: `正在编辑内容 ID: ${id}`,
    });
  };

  const handleDelete = (id: number) => {
    toast({
      title: "删除内容",
      description: "内容已成功删除",
    });
  };

  const handleReschedule = (id: number) => {
    toast({
      title: "重新安排",
      description: "请选择新的发布时间",
    });
  };

  const handleDuplicate = (id: number) => {
    toast({
      title: "复制内容",
      description: "内容已复制到草稿箱",
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">发布管理</h2>
          <p className="text-gray-600">管理您的内容发布计划和历史记录</p>
        </div>
        <div className="flex space-x-3">
          <Button variant="outline">
            <Plus className="w-4 h-4 mr-2" />
            批量操作
          </Button>
          <Button className="bg-blue-600 hover:bg-blue-700">
            <Plus className="w-4 h-4 mr-2" />
            新建内容
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card className="border-0 shadow-sm">
        <CardContent className="p-4">
          <div className="flex flex-wrap items-center gap-4">
            <div className="flex-1 min-w-64">
              <div className="relative">
                <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <Input
                  placeholder="搜索内容..."
                  className="pl-10"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部状态</SelectItem>
                <SelectItem value="scheduled">待发布</SelectItem>
                <SelectItem value="published">已发布</SelectItem>
                <SelectItem value="draft">草稿</SelectItem>
              </SelectContent>
            </Select>
            <Select value={platformFilter} onValueChange={setPlatformFilter}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="平台" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部平台</SelectItem>
                <SelectItem value="instagram">Instagram</SelectItem>
                <SelectItem value="tiktok">TikTok</SelectItem>
                <SelectItem value="linkedin">LinkedIn</SelectItem>
                <SelectItem value="twitter">Twitter</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline">
              <Filter className="w-4 h-4 mr-2" />
              筛选
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="scheduled" className="flex items-center">
            <Clock className="w-4 h-4 mr-2" />
            待发布 ({scheduledPosts.length})
          </TabsTrigger>
          <TabsTrigger value="published" className="flex items-center">
            <CheckCircle className="w-4 h-4 mr-2" />
            已发布 ({publishedPosts.length})
          </TabsTrigger>
          <TabsTrigger value="drafts" className="flex items-center">
            <Edit className="w-4 h-4 mr-2" />
            草稿 ({draftPosts.length})
          </TabsTrigger>
        </TabsList>

        {/* Scheduled Posts */}
        <TabsContent value="scheduled" className="space-y-4">
          {scheduledPosts.map((post) => (
            <Card key={post.id} className="border-0 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-3">
                      <Badge className={getStatusColor(post.status)}>
                        {getStatusText(post.status)}
                      </Badge>
                      <div className="flex items-center space-x-1 text-sm text-gray-600">
                        <Clock className="w-4 h-4" />
                        <span>{post.scheduledTime}</span>
                      </div>
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">{post.title}</h3>
                    <p className="text-gray-600 mb-4 line-clamp-2">{post.excerpt}</p>
                    <div className="flex items-center space-x-4">
                      <div className="flex space-x-2">
                        {post.platforms.map((platform, idx) => {
                          const Icon = getPlatformIcon(platform);
                          return (
                            <div key={idx} className={`flex items-center space-x-1 px-2 py-1 rounded-full ${getPlatformColor(platform)} text-white text-xs`}>
                              <Icon className="w-3 h-3" />
                              <span>{platform}</span>
                            </div>
                          );
                        })}
                      </div>
                      <div className="flex space-x-1">
                        {post.tags.map((tag, idx) => (
                          <Badge key={idx} variant="outline" className="text-xs">
                            #{tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-col space-y-2">
                    <Button variant="ghost" size="sm" onClick={() => handleEdit(post.id)}>
                      <Edit className="w-4 h-4 mr-2" />
                      编辑
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => handleReschedule(post.id)}>
                      <Clock className="w-4 h-4 mr-2" />
                      重新安排
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => handleDelete(post.id)}>
                      <Trash2 className="w-4 h-4 mr-2" />
                      取消
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        {/* Published Posts */}
        <TabsContent value="published" className="space-y-4">
          {publishedPosts.map((post) => (
            <Card key={post.id} className="border-0 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-3">
                      <Badge className={getStatusColor(post.status)}>
                        {getStatusText(post.status)}
                      </Badge>
                      <div className="flex items-center space-x-1 text-sm text-gray-600">
                        <CheckCircle className="w-4 h-4" />
                        <span>{post.publishedTime}</span>
                      </div>
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">{post.title}</h3>
                    <p className="text-gray-600 mb-4 line-clamp-2">{post.excerpt}</p>
                    <div className="flex items-center space-x-4 mb-4">
                      <div className="flex space-x-2">
                        {post.platforms.map((platform, idx) => {
                          const Icon = getPlatformIcon(platform);
                          return (
                            <div key={idx} className={`flex items-center space-x-1 px-2 py-1 rounded-full ${getPlatformColor(platform)} text-white text-xs`}>
                              <Icon className="w-3 h-3" />
                              <span>{platform}</span>
                            </div>
                          );
                        })}
                      </div>
                      <div className="flex space-x-1">
                        {post.tags.map((tag, idx) => (
                          <Badge key={idx} variant="outline" className="text-xs">
                            #{tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    {post.performance && (
                      <div className="grid grid-cols-2 md:grid-cols-5 gap-4 p-4 bg-gray-50 rounded-lg">
                        <div className="text-center">
                          <div className="flex items-center justify-center space-x-1">
                            <Heart className="w-4 h-4 text-red-500" />
                            <span className="font-medium">{post.performance.likes}</span>
                          </div>
                          <p className="text-xs text-gray-500 mt-1">点赞</p>
                        </div>
                        <div className="text-center">
                          <div className="flex items-center justify-center space-x-1">
                            <MessageCircle className="w-4 h-4 text-blue-500" />
                            <span className="font-medium">{post.performance.comments}</span>
                          </div>
                          <p className="text-xs text-gray-500 mt-1">评论</p>
                        </div>
                        <div className="text-center">
                          <div className="flex items-center justify-center space-x-1">
                            <Share2 className="w-4 h-4 text-green-500" />
                            <span className="font-medium">{post.performance.shares}</span>
                          </div>
                          <p className="text-xs text-gray-500 mt-1">分享</p>
                        </div>
                        <div className="text-center">
                          <div className="flex items-center justify-center space-x-1">
                            <Eye className="w-4 h-4 text-purple-500" />
                            <span className="font-medium">{post.performance.reach.toLocaleString()}</span>
                          </div>
                          <p className="text-xs text-gray-500 mt-1">覆盖</p>
                        </div>
                        <div className="text-center">
                          <div className="flex items-center justify-center space-x-1">
                            <BarChart3 className="w-4 h-4 text-orange-500" />
                            <span className="font-medium">{post.performance.engagement}%</span>
                          </div>
                          <p className="text-xs text-gray-500 mt-1">互动率</p>
                        </div>
                      </div>
                    )}
                  </div>
                  <div className="flex flex-col space-y-2">
                    <Button variant="ghost" size="sm">
                      <BarChart3 className="w-4 h-4 mr-2" />
                      查看数据
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => handleDuplicate(post.id)}>
                      <Copy className="w-4 h-4 mr-2" />
                      复制
                    </Button>
                    <Button variant="ghost" size="sm">
                      <RefreshCw className="w-4 h-4 mr-2" />
                      重新发布
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        {/* Draft Posts */}
        <TabsContent value="drafts" className="space-y-4">
          {draftPosts.map((post) => (
            <Card key={post.id} className="border-0 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-3">
                      <Badge className={getStatusColor(post.status)}>
                        {getStatusText(post.status)}
                      </Badge>
                      <div className="flex items-center space-x-1 text-sm text-gray-600">
                        <Edit className="w-4 h-4" />
                        <span>最后修改: {post.lastModified}</span>
                      </div>
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">{post.title}</h3>
                    <p className="text-gray-600 mb-4 line-clamp-2">{post.excerpt}</p>
                    <div className="flex items-center space-x-4">
                      <div className="flex space-x-1">
                        {post.tags.map((tag, idx) => (
                          <Badge key={idx} variant="outline" className="text-xs">
                            #{tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                  <div className="flex flex-col space-y-2">
                    <Button variant="ghost" size="sm" onClick={() => handleEdit(post.id)}>
                      <Edit className="w-4 h-4 mr-2" />
                      编辑
                    </Button>
                    <Button variant="ghost" size="sm">
                      <Send className="w-4 h-4 mr-2" />
                      发布
                    </Button>
                    <Button variant="ghost" size="sm" onClick={() => handleDelete(post.id)}>
                      <Trash2 className="w-4 h-4 mr-2" />
                      删除
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default PublishManager;
