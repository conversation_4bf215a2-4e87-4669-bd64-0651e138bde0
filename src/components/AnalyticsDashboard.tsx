
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  Target, 
  Heart,
  MessageCircle,
  Share2,
  Eye,
  Calendar,
  Download,
  Filter,
  Instagram,
  Twitter,
  Linkedin,
  Youtube
} from 'lucide-react';

const AnalyticsDashboard = () => {
  const stats = [
    { title: "总互动量", value: "12.5K", change: "+15%", trend: "up", icon: Heart, color: "text-red-500" },
    { title: "覆盖人数", value: "45.2K", change: "+8%", trend: "up", icon: Users, color: "text-blue-500" },
    { title: "转化率", value: "3.2%", change: "+0.5%", trend: "up", icon: Target, color: "text-green-500" },
    { title: "平均互动率", value: "4.8%", change: "-0.2%", trend: "down", icon: BarChart3, color: "text-purple-500" },
  ];

  const topPosts = [
    {
      id: 1,
      title: "5个提升Instagram互动率的技巧",
      platform: "Instagram",
      date: "2024-01-20",
      likes: 1234,
      comments: 89,
      shares: 45,
      reach: 15678,
      engagement: 8.5,
      icon: Instagram,
      color: "bg-pink-500"
    },
    {
      id: 2,
      title: "TikTok算法解密：如何获得更多曝光",
      platform: "TikTok",
      date: "2024-01-18",
      likes: 987,
      comments: 156,
      shares: 67,
      reach: 12456,
      engagement: 7.2,
      icon: Youtube,
      color: "bg-red-500"
    },
    {
      id: 3,
      title: "LinkedIn个人品牌建设完整指南",
      platform: "LinkedIn",
      date: "2024-01-16",
      likes: 756,
      comments: 234,
      shares: 123,
      reach: 18234,
      engagement: 6.8,
      icon: Linkedin,
      color: "bg-blue-600"
    },
    {
      id: 4,
      title: "Twitter营销策略分析",
      platform: "Twitter",
      date: "2024-01-14",
      likes: 432,
      comments: 67,
      shares: 89,
      reach: 8976,
      engagement: 5.4,
      icon: Twitter,
      color: "bg-blue-400"
    }
  ];

  const platformStats = [
    { platform: "Instagram", followers: "15.2K", engagement: "8.2%", growth: "+156", color: "bg-pink-500", icon: Instagram },
    { platform: "TikTok", followers: "8.7K", engagement: "12.4%", growth: "+89", color: "bg-red-500", icon: Youtube },
    { platform: "LinkedIn", followers: "3.2K", engagement: "6.1%", growth: "+34", color: "bg-blue-600", icon: Linkedin },
    { platform: "Twitter", followers: "5.1K", engagement: "4.8%", growth: "+23", color: "bg-blue-400", icon: Twitter }
  ];

  const audienceData = [
    { age: "18-24", percentage: 25, color: "bg-blue-500" },
    { age: "25-34", percentage: 35, color: "bg-green-500" },
    { age: "35-44", percentage: 20, color: "bg-yellow-500" },
    { age: "45-54", percentage: 15, color: "bg-red-500" },
    { age: "55+", percentage: 5, color: "bg-gray-500" }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">数据分析中心</h2>
          <p className="text-gray-600">深入了解您的内容表现和受众洞察</p>
        </div>
        <div className="flex space-x-3">
          <Select defaultValue="30days">
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7days">最近7天</SelectItem>
              <SelectItem value="30days">最近30天</SelectItem>
              <SelectItem value="90days">最近90天</SelectItem>
              <SelectItem value="1year">最近1年</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline">
            <Filter className="w-4 h-4 mr-2" />
            筛选
          </Button>
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            导出报告
          </Button>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <Card key={index} className="border-0 shadow-sm">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                  <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                  <div className="flex items-center mt-2">
                    <TrendingUp className={`w-4 h-4 mr-1 ${stat.trend === 'up' ? 'text-green-500' : 'text-red-500'}`} />
                    <span className={`text-sm ${stat.trend === 'up' ? 'text-green-600' : 'text-red-600'}`}>
                      {stat.change}
                    </span>
                  </div>
                </div>
                <div className={`p-3 rounded-full bg-gray-100 ${stat.color}`}>
                  <stat.icon className="w-6 h-6" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Chart Section */}
      <Card className="border-0 shadow-sm">
        <CardHeader>
          <CardTitle className="flex items-center">
            <BarChart3 className="w-5 h-5 mr-2" />
            互动趋势分析
          </CardTitle>
          <CardDescription>过去30天的互动数据变化</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-80 flex items-center justify-center bg-gray-50 rounded-lg">
            <div className="text-center">
              <BarChart3 className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 font-medium">互动趋势图表</p>
              <p className="text-sm text-gray-500 mt-1">展示各平台互动数据的时间趋势</p>
              <div className="flex justify-center space-x-4 mt-4">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-pink-500 rounded-full"></div>
                  <span className="text-sm text-gray-600">Instagram</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <span className="text-sm text-gray-600">TikTok</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-blue-600 rounded-full"></div>
                  <span className="text-sm text-gray-600">LinkedIn</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-blue-400 rounded-full"></div>
                  <span className="text-sm text-gray-600">Twitter</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Top Performing Posts */}
        <div className="lg:col-span-2">
          <Card className="border-0 shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center">
                <TrendingUp className="w-5 h-5 mr-2" />
                内容表现排行榜
              </CardTitle>
              <CardDescription>最佳表现的内容分析</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {topPosts.map((post) => (
                <div key={post.id} className="p-4 rounded-lg border border-gray-200 hover:shadow-md transition-shadow">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <div className={`p-2 rounded-lg ${post.color} text-white`}>
                          <post.icon className="w-4 h-4" />
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900">{post.title}</h4>
                          <p className="text-sm text-gray-500">{post.date}</p>
                        </div>
                      </div>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-3">
                        <div className="text-center">
                          <div className="flex items-center justify-center space-x-1">
                            <Heart className="w-4 h-4 text-red-500" />
                            <span className="text-sm font-medium">{post.likes.toLocaleString()}</span>
                          </div>
                          <p className="text-xs text-gray-500 mt-1">点赞</p>
                        </div>
                        <div className="text-center">
                          <div className="flex items-center justify-center space-x-1">
                            <MessageCircle className="w-4 h-4 text-blue-500" />
                            <span className="text-sm font-medium">{post.comments}</span>
                          </div>
                          <p className="text-xs text-gray-500 mt-1">评论</p>
                        </div>
                        <div className="text-center">
                          <div className="flex items-center justify-center space-x-1">
                            <Share2 className="w-4 h-4 text-green-500" />
                            <span className="text-sm font-medium">{post.shares}</span>
                          </div>
                          <p className="text-xs text-gray-500 mt-1">分享</p>
                        </div>
                        <div className="text-center">
                          <div className="flex items-center justify-center space-x-1">
                            <Eye className="w-4 h-4 text-purple-500" />
                            <span className="text-sm font-medium">{post.reach.toLocaleString()}</span>
                          </div>
                          <p className="text-xs text-gray-500 mt-1">覆盖</p>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <Badge variant="secondary" className="mb-2">
                        {post.engagement}% 互动率
                      </Badge>
                      <Button variant="ghost" size="sm">
                        查看详情
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>

        {/* Right Sidebar */}
        <div className="space-y-6">
          {/* Platform Performance */}
          <Card className="border-0 shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart3 className="w-5 h-5 mr-2" />
                平台表现
              </CardTitle>
              <CardDescription>各平台数据对比</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {platformStats.map((platform, index) => (
                <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-gray-50">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg ${platform.color} text-white`}>
                      <platform.icon className="w-4 h-4" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{platform.platform}</p>
                      <p className="text-sm text-gray-600">{platform.followers} 关注者</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900">{platform.engagement}</p>
                    <p className="text-xs text-green-600">{platform.growth}</p>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Audience Demographics */}
          <Card className="border-0 shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="w-5 h-5 mr-2" />
                受众分析
              </CardTitle>
              <CardDescription>年龄分布</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {audienceData.map((age, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-900">{age.age}</span>
                    <span className="text-sm text-gray-600">{age.percentage}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${age.color}`}
                      style={{ width: `${age.percentage}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card className="border-0 shadow-sm">
            <CardHeader>
              <CardTitle>快速操作</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button variant="outline" className="w-full justify-start">
                <Download className="w-4 h-4 mr-2" />
                下载完整报告
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Calendar className="w-4 h-4 mr-2" />
                安排分析会议
              </Button>
              <Button variant="outline" className="w-full justify-start">
                <Target className="w-4 h-4 mr-2" />
                设置目标指标
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default AnalyticsDashboard;
